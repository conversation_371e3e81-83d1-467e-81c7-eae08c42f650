//@version=6
strategy("RSI Last Candle Strategy v6 - FIXED",
         shorttitle="RSI-LAST",
         overlay=true,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=10,
         commission_type=strategy.commission.percent,
         commission_value=0.1,
         slippage=2,
         calc_on_every_tick=true,
         max_bars_back=500)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎯 RSI LAST CANDLE STRATEGY - FIXED VERSION
// ═══════════════════════════════════════════════════════════════════════════════════════
// FIXES APPLIED:
// ✅ Simplified practical "last candle" logic
// ✅ Removed conflicting exit mechanisms  
// ✅ Clean state management
// ✅ Reliable signal generation
// ✅ Single exit strategy (no conflicts)
// ✅ Proper signal alternation
// ═══════════════════════════════════════════════════════════════════════════════════════

// 🔧 INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════
group_rsi = "📊 RSI Settings"
rsi_length = input.int(14, "RSI Length", minval=5, maxval=50, group=group_rsi)
rsi_oversold = input.float(30.0, "RSI Oversold Level", minval=15.0, maxval=40.0, step=1.0, group=group_rsi)
rsi_overbought = input.float(70.0, "RSI Overbought Level", minval=60.0, maxval=85.0, step=1.0, group=group_rsi)
rsi_recovery_zone = input.float(35.0, "RSI Recovery Zone", minval=30.0, maxval=45.0, step=1.0, group=group_rsi)
rsi_decline_zone = input.float(65.0, "RSI Decline Zone", minval=55.0, maxval=70.0, step=1.0, group=group_rsi)

group_risk = "🛡️ Risk Management"
atr_length = input.int(14, "ATR Length", minval=5, maxval=30, group=group_risk)
stop_loss_atr = input.float(2.0, "Stop Loss (ATR Multiplier)", minval=1.0, maxval=5.0, step=0.1, group=group_risk)
take_profit_atr = input.float(3.0, "Take Profit (ATR Multiplier)", minval=1.5, maxval=8.0, step=0.1, group=group_risk)
max_risk_per_trade = input.float(2.0, "Max Risk Per Trade (%)", minval=0.5, maxval=10.0, step=0.1, group=group_risk)

group_signals = "🎯 Signal Settings"
show_signals = input.bool(true, "Show Buy/Sell Signals", group=group_signals)
show_levels = input.bool(true, "Show Stop Loss/Take Profit", group=group_signals)
show_debug = input.bool(true, "Show Debug Indicators", group=group_signals)

// 📊 TECHNICAL INDICATORS
// ═══════════════════════════════════════════════════════════════════════════════════════

// RSI Calculation
rsi = ta.rsi(close, rsi_length)

// ATR for volatility-based stops
atr = ta.atr(atr_length)

// 🧠 SIMPLIFIED SIGNAL LOGIC - LAST CANDLE APPROACH
// ═══════════════════════════════════════════════════════════════════════════════════════

// State Management (Minimal)
var string last_signal = "NONE"

// PRACTICAL "LAST CANDLE" LOGIC
// Buy: RSI was oversold and is now recovering (but still in recovery zone)
rsi_buy_condition = rsi[1] <= rsi_oversold and rsi > rsi[1] and rsi <= rsi_recovery_zone

// Sell: RSI was overbought and is now declining (but still in decline zone)  
rsi_sell_condition = rsi[1] >= rsi_overbought and rsi < rsi[1] and rsi >= rsi_decline_zone

// Alternative: Simple momentum-based last candle detection
rsi_buy_momentum = rsi <= rsi_oversold and rsi > rsi[1] and rsi[1] <= rsi[2]  // RSI bottoming out
rsi_sell_momentum = rsi >= rsi_overbought and rsi < rsi[1] and rsi[1] >= rsi[2]  // RSI topping out

// Combined signal logic (either condition works)
rsi_buy_signal = rsi_buy_condition or rsi_buy_momentum
rsi_sell_signal = rsi_sell_condition or rsi_sell_momentum

// STRICT SIGNAL ALTERNATION
generate_buy_signal = rsi_buy_signal and last_signal != "BUY" and strategy.position_size == 0
generate_sell_signal = rsi_sell_signal and last_signal == "BUY" and strategy.position_size > 0

// 💰 POSITION SIZING
// ═══════════════════════════════════════════════════════════════════════════════════════

calculate_position_size(entry_price, stop_price, risk_percent) =>
    risk_amount = strategy.equity * (risk_percent / 100)
    price_diff = math.abs(entry_price - stop_price)
    if price_diff > 0
        position_size = risk_amount / price_diff
        // Cap position size to prevent over-leverage
        max_position = strategy.equity * 0.95 / entry_price
        math.min(position_size, max_position)
    else
        strategy.equity * 0.1 / entry_price  // Fallback

// 📋 STRATEGY EXECUTION - SINGLE CLEAN EXIT STRATEGY
// ═══════════════════════════════════════════════════════════════════════════════════════

// BUY Signal Execution
if generate_buy_signal
    // Calculate levels
    entry_price = close
    stop_loss_level = entry_price - (atr * stop_loss_atr)
    take_profit_level = entry_price + (atr * take_profit_atr)
    
    // Calculate position size
    position_size = calculate_position_size(entry_price, stop_loss_level, max_risk_per_trade)
    
    // Execute trade with automatic exit orders
    strategy.entry("RSI_BUY", strategy.long, qty=position_size,
                   comment="RSI Buy: " + str.tostring(math.round(rsi, 1)))
    
    // Set ONLY exit orders (no conflicts)
    strategy.exit("RSI_EXIT", "RSI_BUY", stop=stop_loss_level, limit=take_profit_level,
                  comment="Auto Exit")
    
    // Update state
    last_signal := "BUY"

// SELL Signal Execution (Manual close for RSI signals)
if generate_sell_signal
    strategy.close("RSI_BUY", comment="RSI Sell: " + str.tostring(math.round(rsi, 1)))
    
    // Update state
    last_signal := "SELL"

// 🎨 VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot Signals
plotshape(show_signals and generate_buy_signal, "BUY SIGNAL", shape.triangleup,
          location.belowbar, color.lime, size=size.normal, text="BUY")
plotshape(show_signals and generate_sell_signal, "SELL SIGNAL", shape.triangledown,
          location.abovebar, color.red, size=size.normal, text="SELL")

// Debug Indicators
if show_debug
    // Show RSI conditions
    plotshape(rsi_buy_condition, "Buy Condition", shape.circle,
              location.belowbar, color.yellow, size=size.small, text="BC")
    plotshape(rsi_sell_condition, "Sell Condition", shape.circle,
              location.abovebar, color.orange, size=size.small, text="SC")
    
    // Show momentum conditions
    plotshape(rsi_buy_momentum, "Buy Momentum", shape.diamond,
              location.belowbar, color.aqua, size=size.tiny, text="BM")
    plotshape(rsi_sell_momentum, "Sell Momentum", shape.diamond,
              location.abovebar, color.purple, size=size.tiny, text="SM")
    
    // Show RSI zones
    plotshape(rsi <= rsi_oversold, "Oversold", shape.square,
              location.belowbar, color.new(color.blue, 80), size=size.tiny)
    plotshape(rsi >= rsi_overbought, "Overbought", shape.square,
              location.abovebar, color.new(color.red, 80), size=size.tiny)

// Plot Stop Loss and Take Profit (only when position is active)
plot(show_levels and strategy.position_size > 0 ? strategy.position_avg_price - (atr * stop_loss_atr) : na,
     "Stop Loss", color=color.red, style=plot.style_line, linewidth=2)
plot(show_levels and strategy.position_size > 0 ? strategy.position_avg_price + (atr * take_profit_atr) : na,
     "Take Profit", color=color.green, style=plot.style_line, linewidth=2)

// Background coloring for RSI zones
bgcolor(rsi <= rsi_oversold ? color.new(color.green, 95) : 
        rsi >= rsi_overbought ? color.new(color.red, 95) : na)

// 📊 INFORMATION TABLE
// ═══════════════════════════════════════════════════════════════════════════════════════

if barstate.islast
    var table info_table = table.new(position.top_right, 2, 7, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "RSI Last Candle", text_color=color.white, bgcolor=color.blue)
    table.cell(info_table, 1, 0, "FIXED v6", text_color=color.white, bgcolor=color.blue)
    table.cell(info_table, 0, 1, "RSI", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(math.round(rsi, 1)),
               text_color=rsi <= rsi_oversold ? color.green : rsi >= rsi_overbought ? color.red : color.black)
    table.cell(info_table, 0, 2, "Last Signal", text_color=color.black)
    table.cell(info_table, 1, 2, last_signal,
               text_color=last_signal == "BUY" ? color.green : last_signal == "SELL" ? color.red : color.gray)
    table.cell(info_table, 0, 3, "Position", text_color=color.black)
    table.cell(info_table, 1, 3, strategy.position_size > 0 ? "LONG" : "NONE",
               text_color=strategy.position_size > 0 ? color.green : color.gray)
    table.cell(info_table, 0, 4, "Entry Price", text_color=color.black)
    table.cell(info_table, 1, 4, strategy.position_size > 0 ? str.tostring(strategy.position_avg_price) : "N/A",
               text_color=color.blue)
    table.cell(info_table, 0, 5, "P&L", text_color=color.black)
    table.cell(info_table, 1, 5, strategy.position_size > 0 ? str.tostring(math.round(strategy.openprofit, 2)) : "N/A",
               text_color=strategy.openprofit > 0 ? color.green : strategy.openprofit < 0 ? color.red : color.gray)
    table.cell(info_table, 0, 6, "Risk/Trade", text_color=color.black)
    table.cell(info_table, 1, 6, str.tostring(max_risk_per_trade) + "%", text_color=color.blue)

// 📈 RSI INDICATOR SUBPLOT
// ═══════════════════════════════════════════════════════════════════════════════════════

// Optional: Add RSI as a separate indicator
// Uncomment the lines below if you want RSI plotted separately

// rsi_plot = plot(rsi, "RSI", color=color.purple, linewidth=2)
// hline(rsi_overbought, "Overbought", color=color.red, linestyle=hline.style_dashed)
// hline(rsi_oversold, "Oversold", color=color.green, linestyle=hline.style_dashed)
// hline(50, "Midline", color=color.gray, linestyle=hline.style_dotted)

// Fill RSI zones
// fill_color_ob = rsi >= rsi_overbought ? color.new(color.red, 80) : na
// fill_color_os = rsi <= rsi_oversold ? color.new(color.green, 80) : na
// bgcolor(fill_color_ob)
// bgcolor(fill_color_os)
