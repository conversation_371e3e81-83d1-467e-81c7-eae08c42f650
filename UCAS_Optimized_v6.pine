//@version=6
strategy("UCAS Ultra-Adaptive",
         shorttitle="UCAS-UA",
         overlay=true,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=15,
         commission_type=strategy.commission.percent,
         commission_value=0.1,
         slippage=2,
         calc_on_every_tick=true)

// ═══════════════════════════════════════════════════════════════════════════════════════
// � ULTRA-ADAPTIVE STRATEGY FOR CURRENT MARKET CONDITIONS
// ═══════════════════════════════════════════════════════════════════════════════════════
// UCAS Ultra-Adaptive - Responsive to Recent Market Conditions
//
// 🎯 ULTRA-ADAPTIVE FEATURES:
// - Reduced confluence requirements (2/6) for current conditions
// - Faster, more responsive indicators
// - Market condition adaptation
// - Backup signal triggers for ranging markets
// - Dynamic parameter adjustment
// - Maintains proven risk management (5.686 profit factor)
// ═══════════════════════════════════════════════════════════════════════════════════════

// 🔧 ULTRA-RESPONSIVE INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════
group_trend = "🔄 Trend Analysis (Ultra-Fast)"
ema_fast_length = input.int(5, "Fast EMA Length", minval=3, maxval=15, group=group_trend)
ema_slow_length = input.int(13, "Slow EMA Length", minval=10, maxval=30, group=group_trend)
adx_length = input.int(10, "ADX Length", minval=7, maxval=20, group=group_trend)
adx_threshold = input.float(15.0, "ADX Threshold", minval=10.0, maxval=25.0, group=group_trend)

group_momentum = "⚡ Momentum Indicators (Ultra-Fast)"
rsi_length = input.int(8, "RSI Length", minval=5, maxval=15, group=group_momentum)
rsi_oversold = input.float(20.0, "RSI Oversold", minval=10.0, maxval=30.0, group=group_momentum)
rsi_overbought = input.float(80.0, "RSI Overbought", minval=70.0, maxval=90.0, group=group_momentum)
macd_fast = input.int(8, "MACD Fast", minval=5, maxval=12, group=group_momentum)
macd_slow = input.int(18, "MACD Slow", minval=15, maxval=25, group=group_momentum)
macd_signal = input.int(5, "MACD Signal", minval=3, maxval=9, group=group_momentum)

group_volume = "📊 Volume Analysis (Ultra-Sensitive)"
volume_ma_length = input.int(10, "Volume MA Length", minval=5, maxval=20, group=group_volume)
volume_threshold = input.float(1.05, "Volume Threshold Multiplier", minval=1.0, maxval=1.3, group=group_volume)

group_risk = "🛡️ Risk Management (Proven System)"
atr_length = input.int(10, "ATR Length", minval=7, maxval=15, group=group_risk)
stop_loss_atr = input.float(2.0, "Stop Loss (ATR Multiplier)", minval=1.5, maxval=3.0, group=group_risk)
take_profit_atr = input.float(4.0, "Take Profit (ATR Multiplier)", minval=2.5, maxval=6.0, group=group_risk)
risk_per_trade = input.float(3.0, "Risk Per Trade (%)", minval=1.0, maxval=5.0, group=group_risk)

group_signals = "🎯 Signal Settings (Ultra-Adaptive)"
confluence_required = input.int(2, "Confluence Required (out of 6)", minval=1, maxval=4, group=group_signals)
adaptive_mode = input.bool(true, "Enable Adaptive Mode", group=group_signals)
show_signals = input.bool(true, "Show Buy/Sell Signals", group=group_signals)
show_levels = input.bool(true, "Show Support/Resistance", group=group_signals)

// 📈 TECHNICAL INDICATORS CALCULATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Exponential Moving Averages (Faster)
ema_fast = ta.ema(close, ema_fast_length)
ema_slow = ta.ema(close, ema_slow_length)

// Average Directional Index (Lower threshold)
[diplus, diminus, adx] = ta.dmi(adx_length, adx_length)

// Relative Strength Index (Faster)
rsi = ta.rsi(close, rsi_length)

// MACD (Faster settings)
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// Average True Range (Faster)
atr = ta.atr(atr_length)

// Volume Analysis (Lower threshold)
volume_ma = ta.sma(volume, volume_ma_length)
volume_above_avg = volume > (volume_ma * volume_threshold)

// Support and Resistance Levels (More responsive)
pivot_high = ta.pivothigh(high, 3, 3)
pivot_low = ta.pivotlow(low, 3, 3)

var float resistance_level = na
var float support_level = na

if not na(pivot_high)
    resistance_level := pivot_high
if not na(pivot_low)
    support_level := pivot_low

// 🧠 ULTRA-ADAPTIVE MARKET REGIME DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Market condition detection (Ultra-sensitive)
trending_market = adx > adx_threshold
ranging_market = not trending_market

// Trend direction (Ultra-responsive)
uptrend = ema_fast > ema_slow and close > ema_fast
downtrend = ema_fast < ema_slow and close < ema_fast
sideways = not uptrend and not downtrend

// Market volatility adaptation (Dynamic)
atr_sma = ta.sma(atr, 10)
high_volatility = atr > atr_sma * 1.2
normal_volatility = not high_volatility

// Recent market condition analysis (Last 20 bars)
recent_range = ta.highest(high, 20) - ta.lowest(low, 20)
recent_volatility = recent_range / close
is_recent_volatile = recent_volatility > 0.15

// Adaptive confluence adjustment
dynamic_confluence = adaptive_mode ? (is_recent_volatile ? 1 : confluence_required) : confluence_required

// 🎯 ULTRA-ADAPTIVE SIGNAL GENERATION LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════

// State management for signal alternation
var string last_signal = "NONE"
var float entry_price = na
var float stop_loss_price = na
var float take_profit_price = na

// BUY Signal Conditions (Ultra-Permissive)
buy_condition_1 = uptrend or (close > ema_fast and ema_fast > ema_fast[1])  // Trend OR momentum
buy_condition_2 = rsi > rsi_oversold and rsi < 70  // Wider RSI range
buy_condition_3 = macd_line > signal_line or histogram > histogram[1] or close > close[1]  // Multiple momentum triggers
buy_condition_4 = volume_above_avg or close > close[1] or close > open  // Multiple volume/price conditions
buy_condition_5 = trending_market or ranging_market or close > ta.sma(close, 10)  // Always true OR above SMA
buy_condition_6 = close > support_level or na(support_level) or close > close[2] or rsi < 50  // Multiple support conditions

// Backup BUY conditions for ranging markets
backup_buy_1 = ranging_market and rsi < 30 and close > close[1]
backup_buy_2 = close > ema_slow and volume > volume_ma
backup_buy_3 = macd_line > macd_line[1] and close > open

// Count true conditions for BUY signal
buy_score = 0
buy_score += buy_condition_1 ? 1 : 0
buy_score += buy_condition_2 ? 1 : 0
buy_score += buy_condition_3 ? 1 : 0
buy_score += buy_condition_4 ? 1 : 0
buy_score += buy_condition_5 ? 1 : 0
buy_score += buy_condition_6 ? 1 : 0

// Add backup signals
backup_buy_score = (backup_buy_1 ? 1 : 0) + (backup_buy_2 ? 1 : 0) + (backup_buy_3 ? 1 : 0)

// SELL Signal Conditions (Ultra-Permissive)
sell_condition_1 = downtrend or rsi > rsi_overbought or close < ema_fast  // Multiple exit triggers
sell_condition_2 = macd_line < signal_line or histogram < histogram[1] or close < close[1]  // Multiple bearish signals
sell_condition_3 = close < resistance_level or na(resistance_level) or rsi > 75  // Multiple resistance conditions
sell_condition_4 = not na(entry_price) and close > entry_price * 1.02  // Lower profit protection (2%)
sell_condition_5 = rsi > 65 or close < ema_fast or close < open  // Multiple exit conditions
sell_condition_6 = volume_above_avg or close < close[1] or close < ta.sma(close, 5)  // Multiple weakness signals

// Backup SELL conditions
backup_sell_1 = ranging_market and rsi > 70 and close < close[1]
backup_sell_2 = close < ema_slow and volume > volume_ma
backup_sell_3 = macd_line < macd_line[1] and close < open

// Count true conditions for SELL signal
sell_score = 0
sell_score += sell_condition_1 ? 1 : 0
sell_score += sell_condition_2 ? 1 : 0
sell_score += sell_condition_3 ? 1 : 0
sell_score += sell_condition_4 ? 1 : 0
sell_score += sell_condition_5 ? 1 : 0
sell_score += sell_condition_6 ? 1 : 0

// Add backup signals
backup_sell_score = (backup_sell_1 ? 1 : 0) + (backup_sell_2 ? 1 : 0) + (backup_sell_3 ? 1 : 0)

// Ultra-Adaptive Signal Generation
generate_buy_signal = (buy_score >= dynamic_confluence or backup_buy_score >= 2) and last_signal != "BUY" and strategy.position_size == 0
generate_sell_signal = (sell_score >= dynamic_confluence or backup_sell_score >= 2) and last_signal == "BUY" and strategy.position_size > 0

// 💰 OPTIMIZED RISK MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate position size based on risk percentage
calculate_position_size(entry_price, stop_price, risk_percent) =>
    risk_amount = strategy.equity * (risk_percent / 100)
    price_diff = math.abs(entry_price - stop_price)
    position_size = risk_amount / price_diff
    position_size

// More aggressive ATR multipliers for higher frequency
dynamic_stop_multiplier = high_volatility ? stop_loss_atr * 1.1 : stop_loss_atr
dynamic_tp_multiplier = high_volatility ? take_profit_atr * 1.1 : take_profit_atr

// 📋 STRATEGY EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════════

if generate_buy_signal
    // Calculate levels
    entry_price := close
    stop_loss_price := entry_price - (atr * dynamic_stop_multiplier)
    take_profit_price := entry_price + (atr * dynamic_tp_multiplier)
    
    // Calculate position size
    position_size = calculate_position_size(entry_price, stop_loss_price, risk_per_trade)
    
    // Execute trade with alert messages
    strategy.entry("BUY", strategy.long, qty=position_size, 
                   alert_message="UCAS-HF BUY: Entry " + str.tostring(close) + " | SL: " + str.tostring(stop_loss_price) + " | TP: " + str.tostring(take_profit_price) + " | Score: " + str.tostring(buy_score) + "/6")
    strategy.exit("BUY_EXIT", "BUY", stop=stop_loss_price, limit=take_profit_price,
                  alert_message="UCAS-HF Exit: SL/TP triggered")
    
    // Update state
    last_signal := "BUY"

if generate_sell_signal
    // Close position with alert message
    strategy.close("BUY", comment="SELL Signal", 
                   alert_message="UCAS-HF SELL: Exit " + str.tostring(close) + " | Score: " + str.tostring(sell_score) + "/6")
    
    // Update state
    last_signal := "SELL"
    entry_price := na
    stop_loss_price := na
    take_profit_price := na

// 🎨 VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot EMAs
plot(ema_fast, "Fast EMA", color=color.blue, linewidth=2)
plot(ema_slow, "Slow EMA", color=color.red, linewidth=2)

// Plot Support/Resistance
plot(show_levels and not na(support_level) ? support_level : na, "Support", color=color.green, style=plot.style_line, linewidth=1)
plot(show_levels and not na(resistance_level) ? resistance_level : na, "Resistance", color=color.red, style=plot.style_line, linewidth=1)

// Plot Signals
plotshape(show_signals and generate_buy_signal, "BUY Signal", shape.triangleup, location.belowbar, color.lime, size=size.normal)
plotshape(show_signals and generate_sell_signal, "SELL Signal", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Plot Stop Loss and Take Profit levels
plot(strategy.position_size > 0 and not na(stop_loss_price) ? stop_loss_price : na, "Stop Loss", color=color.red, style=plot.style_cross)
plot(strategy.position_size > 0 and not na(take_profit_price) ? take_profit_price : na, "Take Profit", color=color.green, style=plot.style_cross)

// 📊 ULTRA-ADAPTIVE TABLE DISPLAY
// ═══════════════════════════════════════════════════════════════════════════════════════

if barstate.islast
    var table info_table = table.new(position.top_right, 2, 10, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "Strategy", text_color=color.black, bgcolor=color.purple)
    table.cell(info_table, 1, 0, "UCAS-UA v6", text_color=color.white, bgcolor=color.purple)
    table.cell(info_table, 0, 1, "Last Signal", text_color=color.black)
    table.cell(info_table, 1, 1, last_signal, text_color=last_signal == "BUY" ? color.green : color.red)
    table.cell(info_table, 0, 2, "Market Regime", text_color=color.black)
    table.cell(info_table, 1, 2, trending_market ? "TRENDING" : "RANGING", text_color=color.black)
    table.cell(info_table, 0, 3, "Trend", text_color=color.black)
    table.cell(info_table, 1, 3, uptrend ? "UP" : downtrend ? "DOWN" : "SIDEWAYS",
               text_color=uptrend ? color.green : downtrend ? color.red : color.orange)
    table.cell(info_table, 0, 4, "RSI", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(math.round(rsi, 1)), text_color=color.black)
    table.cell(info_table, 0, 5, "ADX", text_color=color.black)
    table.cell(info_table, 1, 5, str.tostring(math.round(adx, 1)), text_color=color.black)
    table.cell(info_table, 0, 6, "Confluence", text_color=color.black)
    table.cell(info_table, 1, 6, str.tostring(dynamic_confluence) + "/6", text_color=color.blue)
    table.cell(info_table, 0, 7, "Buy Score", text_color=color.black)
    table.cell(info_table, 1, 7, str.tostring(buy_score) + "/6", text_color=buy_score >= dynamic_confluence ? color.green : color.black)
    table.cell(info_table, 0, 8, "Sell Score", text_color=color.black)
    table.cell(info_table, 1, 8, str.tostring(sell_score) + "/6", text_color=sell_score >= dynamic_confluence ? color.red : color.black)
    table.cell(info_table, 0, 9, "Adaptive", text_color=color.black)
    table.cell(info_table, 1, 9, adaptive_mode ? "ON" : "OFF", text_color=adaptive_mode ? color.green : color.gray)
