//@version=6
strategy("RSI Optimized Strategy v6",
         shorttitle="RSI-OPT",
         overlay=true,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=10,
         commission_type=strategy.commission.percent,
         commission_value=0.1,
         slippage=2,
         calc_on_every_tick=true,
         max_bars_back=500)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎯 RSI OPTIMIZED STRATEGY - PINE SCRIPT V6
// ═══════════════════════════════════════════════════════════════════════════════════════
// Features:
// ✅ Strict signal alternation (no duplicate signals)
// ✅ Advanced position management
// ✅ Dynamic stop-loss and take-profit
// ✅ Sideways market adaptation
// ✅ Real-time calculations with calc_on_every_tick=true
// ✅ Compatible with stocks and crypto
// ✅ Comprehensive risk management
// ═══════════════════════════════════════════════════════════════════════════════════════

// 🔧 INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════
group_rsi = "📊 RSI Settings"
rsi_length = input.int(14, "RSI Length", minval=5, maxval=50, group=group_rsi)
rsi_oversold = input.float(30.0, "RSI Oversold Level", minval=10.0, maxval=40.0, step=1.0, group=group_rsi)
rsi_overbought = input.float(70.0, "RSI Overbought Level", minval=60.0, maxval=90.0, step=1.0, group=group_rsi)
rsi_neutral_zone = input.bool(false, "Enable RSI Neutral Zone Filter", group=group_rsi)
rsi_neutral_lower = input.float(40.0, "RSI Neutral Zone Lower", minval=30.0, maxval=50.0, group=group_rsi)
rsi_neutral_upper = input.float(60.0, "RSI Neutral Zone Upper", minval=50.0, maxval=70.0, group=group_rsi)

group_trend = "📈 Trend Filter"
use_trend_filter = input.bool(false, "Enable Trend Filter", group=group_trend)
ema_fast_length = input.int(9, "Fast EMA Length", minval=5, maxval=20, group=group_trend)
ema_slow_length = input.int(21, "Slow EMA Length", minval=15, maxval=50, group=group_trend)

group_risk = "🛡️ Risk Management"
atr_length = input.int(14, "ATR Length", minval=5, maxval=30, group=group_risk)
stop_loss_atr = input.float(2.0, "Stop Loss (ATR Multiplier)", minval=1.0, maxval=5.0, step=0.1, group=group_risk)
take_profit_atr = input.float(3.0, "Take Profit (ATR Multiplier)", minval=1.5, maxval=8.0, step=0.1, group=group_risk)
use_trailing_stop = input.bool(true, "Enable Trailing Stop", group=group_risk)
trailing_stop_atr = input.float(1.5, "Trailing Stop (ATR Multiplier)", minval=0.5, maxval=3.0, step=0.1, group=group_risk)
max_risk_per_trade = input.float(2.0, "Max Risk Per Trade (%)", minval=0.5, maxval=10.0, step=0.1, group=group_risk)

group_position = "💼 Position Management"
max_drawdown_limit = input.float(15.0, "Max Drawdown Limit (%)", minval=5.0, maxval=30.0, step=1.0, group=group_position)
position_sizing_method = input.string("Fixed %", "Position Sizing Method", options=["Fixed %", "Risk-Based", "Volatility-Adjusted"], group=group_position)

group_signals = "🎯 Signal Settings"
show_signals = input.bool(true, "Show Buy/Sell Signals", group=group_signals)
show_levels = input.bool(true, "Show Stop Loss/Take Profit", group=group_signals)
signal_confirmation = input.bool(false, "Require Signal Confirmation", group=group_signals)

// 📊 TECHNICAL INDICATORS
// ═══════════════════════════════════════════════════════════════════════════════════════

// RSI Calculation
rsi = ta.rsi(close, rsi_length)

// Trend Filter EMAs
ema_fast = ta.ema(close, ema_fast_length)
ema_slow = ta.ema(close, ema_slow_length)

// ATR for volatility-based stops
atr = ta.atr(atr_length)

// Market Structure Analysis
higher_high = high > high[1] and high[1] > high[2]
lower_low = low < low[1] and low[1] < low[2]
uptrend = ema_fast > ema_slow
downtrend = ema_fast < ema_slow
sideways = not uptrend and not downtrend

// Volatility Analysis
atr_sma = ta.sma(atr, 20)
high_volatility = atr > atr_sma * 1.3
normal_volatility = not high_volatility

// 🧠 SIGNAL LOGIC WITH STRICT ALTERNATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// State Management Variables
var string last_signal = "NONE"
var float entry_price = na
var float stop_loss_level = na
var float take_profit_level = na
var float trailing_stop_level = na
var bool position_active = false

// RSI LAST CANDLE LOGIC - Buy at last candle of RSI<30, Sell at last candle of RSI>70
// Buy: RSI is below 30 AND RSI is turning up (current RSI > previous RSI)
rsi_buy_signal = rsi <= rsi_oversold and rsi > rsi[1] and rsi[1] <= rsi_oversold

// Sell: RSI is above 70 AND RSI is turning down (current RSI < previous RSI)
rsi_sell_signal = rsi >= rsi_overbought and rsi < rsi[1] and rsi[1] >= rsi_overbought

// Optional Confirmation (disabled by default for more signals)
rsi_buy_confirmed = rsi_buy_signal and (not signal_confirmation or close > close[1])
rsi_sell_confirmed = rsi_sell_signal and (not signal_confirmation or close < close[1])

// Trend Filter Conditions (More Permissive)
trend_allows_buy = not use_trend_filter or uptrend or sideways
trend_allows_sell = not use_trend_filter or downtrend or sideways

// Neutral Zone Filter (More Permissive)
not_in_neutral_zone = not rsi_neutral_zone or rsi <= rsi_neutral_lower or rsi >= rsi_neutral_upper

// SIMPLIFIED Signal Generation with Strict Alternation
generate_buy_signal = rsi_buy_confirmed and trend_allows_buy and not_in_neutral_zone and
                     last_signal != "BUY" and strategy.position_size == 0

generate_sell_signal = (rsi_sell_confirmed and trend_allows_sell and strategy.position_size > 0 and
                       last_signal == "BUY") or
                      (strategy.position_size > 0 and not na(stop_loss_level) and close <= stop_loss_level) or
                      (strategy.position_size > 0 and not na(take_profit_level) and close >= take_profit_level)

// 💰 ADVANCED POSITION SIZING
// ═══════════════════════════════════════════════════════════════════════════════════════

calculate_position_size(entry_price, stop_price, risk_percent) =>
    risk_amount = strategy.equity * (risk_percent / 100)
    price_diff = math.abs(entry_price - stop_price)
    if price_diff > 0
        position_size = risk_amount / price_diff
        // Cap position size to prevent over-leverage
        max_position = strategy.equity * 0.95 / entry_price
        math.min(position_size, max_position)
    else
        strategy.equity * 0.1 / entry_price  // Fallback to 10% of equity

// Volatility-Adjusted Position Sizing
volatility_adjustment = high_volatility ? 0.7 : 1.0  // Reduce size in high volatility

// 🛡️ RISK MANAGEMENT SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════════════

// Dynamic ATR Multipliers based on market conditions
dynamic_stop_multiplier = high_volatility ? stop_loss_atr * 1.2 : stop_loss_atr
dynamic_tp_multiplier = high_volatility ? take_profit_atr * 0.9 : take_profit_atr

// Drawdown Protection - Manual tracking of max equity
var float max_equity_tracked = 0.0
if strategy.equity > max_equity_tracked
    max_equity_tracked := strategy.equity

current_drawdown = max_equity_tracked > 0 ? (strategy.equity - max_equity_tracked) / max_equity_tracked * 100 : 0.0
drawdown_protection = current_drawdown > -max_drawdown_limit

// 📋 STRATEGY EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// BUY Signal Execution
if generate_buy_signal and drawdown_protection
    // Calculate entry levels
    entry_price := close
    stop_loss_level := entry_price - (atr * dynamic_stop_multiplier)
    take_profit_level := entry_price + (atr * dynamic_tp_multiplier)
    trailing_stop_level := stop_loss_level
    
    // Calculate position size based on selected method
    position_size = switch position_sizing_method
        "Fixed %" => strategy.equity * 0.1 / entry_price
        "Risk-Based" => calculate_position_size(entry_price, stop_loss_level, max_risk_per_trade)
        "Volatility-Adjusted" => calculate_position_size(entry_price, stop_loss_level, max_risk_per_trade) * volatility_adjustment
        => strategy.equity * 0.1 / entry_price
    
    // Execute trade
    strategy.entry("RSI_BUY", strategy.long, qty=position_size,
                   comment="RSI Buy: " + str.tostring(math.round(rsi, 1)))
    
    // Set exit orders
    strategy.exit("RSI_EXIT", "RSI_BUY", stop=stop_loss_level, limit=take_profit_level,
                  comment="SL/TP Exit")
    
    // Update state
    last_signal := "BUY"
    position_active := true

// Trailing Stop Logic
if use_trailing_stop and strategy.position_size > 0 and position_active
    new_trailing_stop = close - (atr * trailing_stop_atr)
    if new_trailing_stop > trailing_stop_level
        trailing_stop_level := new_trailing_stop
        strategy.exit("RSI_EXIT", "RSI_BUY", stop=trailing_stop_level, limit=take_profit_level,
                      comment="Trailing Stop")

// SELL Signal Execution (Close Position)
if generate_sell_signal and strategy.position_size > 0
    strategy.close("RSI_BUY", comment="RSI Sell: " + str.tostring(math.round(rsi, 1)))
    
    // Reset state
    last_signal := "SELL"
    position_active := false
    entry_price := na
    stop_loss_level := na
    take_profit_level := na
    trailing_stop_level := na

// Emergency Exit on Excessive Drawdown
if not drawdown_protection and strategy.position_size > 0
    strategy.close_all(comment="Drawdown Protection")
    position_active := false

// 🎨 VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot EMAs
plot(ema_fast, "Fast EMA", color=color.blue, linewidth=1)
plot(ema_slow, "Slow EMA", color=color.red, linewidth=1)

// Plot RSI levels on price chart (scaled)
rsi_price_level = close * (1 + (rsi - 50) / 1000)  // Subtle RSI visualization on price
plot(rsi_price_level, "RSI Level", color=color.purple, linewidth=1, display=display.none)

// Plot Signals
plotshape(show_signals and generate_buy_signal, "BUY", shape.triangleup,
          location.belowbar, color.lime, size=size.normal, text="BUY")
plotshape(show_signals and generate_sell_signal and rsi_sell_confirmed, "SELL", shape.triangledown,
          location.abovebar, color.red, size=size.normal, text="SELL")

// Debug: Show RSI conditions
plotshape(show_signals and rsi_buy_signal, "RSI Buy Last", shape.circle,
          location.belowbar, color.yellow, size=size.small, text="LAST<30")
plotshape(show_signals and rsi_sell_signal, "RSI Sell Last", shape.circle,
          location.abovebar, color.orange, size=size.small, text="LAST>70")

// Debug: Show when RSI is in oversold/overbought zones
plotshape(show_signals and rsi <= rsi_oversold, "RSI Oversold", shape.diamond,
          location.belowbar, color.new(color.blue, 70), size=size.tiny)
plotshape(show_signals and rsi >= rsi_overbought, "RSI Overbought", shape.diamond,
          location.abovebar, color.new(color.purple, 70), size=size.tiny)

// Plot Stop Loss and Take Profit
plot(show_levels and strategy.position_size > 0 ? stop_loss_level : na, 
     "Stop Loss", color=color.red, style=plot.style_line, linewidth=2)
plot(show_levels and strategy.position_size > 0 ? take_profit_level : na, 
     "Take Profit", color=color.green, style=plot.style_line, linewidth=2)
plot(show_levels and use_trailing_stop and strategy.position_size > 0 ? trailing_stop_level : na, 
     "Trailing Stop", color=color.orange, style=plot.style_line, linewidth=1)

// Background coloring for market conditions
bgcolor(uptrend ? color.new(color.green, 95) : downtrend ? color.new(color.red, 95) : color.new(color.gray, 98))

// 📊 INFORMATION TABLE
// ═══════════════════════════════════════════════════════════════════════════════════════

if barstate.islast
    var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "RSI Strategy", text_color=color.white, bgcolor=color.blue)
    table.cell(info_table, 1, 0, "v6.0", text_color=color.white, bgcolor=color.blue)
    table.cell(info_table, 0, 1, "RSI", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(math.round(rsi, 1)), 
               text_color=rsi <= rsi_oversold ? color.green : rsi >= rsi_overbought ? color.red : color.black)
    table.cell(info_table, 0, 2, "Last Signal", text_color=color.black)
    table.cell(info_table, 1, 2, last_signal, 
               text_color=last_signal == "BUY" ? color.green : last_signal == "SELL" ? color.red : color.gray)
    table.cell(info_table, 0, 3, "Position", text_color=color.black)
    table.cell(info_table, 1, 3, position_active ? "ACTIVE" : "NONE", 
               text_color=position_active ? color.green : color.gray)
    table.cell(info_table, 0, 4, "Trend", text_color=color.black)
    table.cell(info_table, 1, 4, uptrend ? "UP" : downtrend ? "DOWN" : "SIDE", 
               text_color=uptrend ? color.green : downtrend ? color.red : color.orange)
    table.cell(info_table, 0, 5, "Volatility", text_color=color.black)
    table.cell(info_table, 1, 5, high_volatility ? "HIGH" : "NORMAL", 
               text_color=high_volatility ? color.red : color.green)
    table.cell(info_table, 0, 6, "Drawdown", text_color=color.black)
    table.cell(info_table, 1, 6, str.tostring(math.round(current_drawdown, 1)) + "%", 
               text_color=current_drawdown < -10 ? color.red : color.black)
    table.cell(info_table, 0, 7, "Risk/Trade", text_color=color.black)
    table.cell(info_table, 1, 7, str.tostring(max_risk_per_trade) + "%", text_color=color.blue)
