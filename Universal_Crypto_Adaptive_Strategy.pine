//@version=6
strategy("Universal Crypto Adaptive Strategy",
         shorttitle="UCAS",
         overlay=true,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=10,
         commission_type=strategy.commission.percent,
         commission_value=0.1,
         slippage=2,
         calc_on_every_tick=true)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📊 STRATEGY DESCRIPTION
// ═══════════════════════════════════════════════════════════════════════════════════════
// Universal Crypto Adaptive Strategy (UCAS) - Pine Script v6
//
// 🎯 TARGET: 10%+ Monthly Returns with Adaptive Risk Management
// 🔄 COMPATIBILITY: All major cryptocurrencies (BTC, ETH, MATIC, etc.)
// 📈 MARKET CONDITIONS: Bull, Bear, and Sideways markets
// ⚡ SIGNAL TYPE: Strict alternating BUY/SELL signals
// 🛡️ RISK MANAGEMENT: ATR-based stops with dynamic position sizing
//
// STRATEGY LOGIC:
// - Multi-layered technical analysis with confluence requirements
// - Adaptive parameters based on market volatility and regime
// - Strict signal alternation to prevent confusion
// - Professional risk management with 2:1+ risk-reward ratios
// - Updated for Pine Script v6 compatibility
// ═══════════════════════════════════════════════════════════════════════════════════════

// 🔧 INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════
group_trend = "🔄 Trend Analysis"
ema_fast_length = input.int(12, "Fast EMA Length", minval=5, maxval=50, group=group_trend)
ema_slow_length = input.int(26, "Slow EMA Length", minval=10, maxval=100, group=group_trend)
adx_length = input.int(14, "ADX Length", minval=10, maxval=30, group=group_trend)
adx_threshold = input.float(25.0, "ADX Threshold", minval=15.0, maxval=40.0, group=group_trend)

group_momentum = "⚡ Momentum Indicators"
rsi_length = input.int(14, "RSI Length", minval=10, maxval=30, group=group_momentum)
rsi_oversold = input.float(30.0, "RSI Oversold", minval=20.0, maxval=40.0, group=group_momentum)
rsi_overbought = input.float(70.0, "RSI Overbought", minval=60.0, maxval=80.0, group=group_momentum)
macd_fast = input.int(12, "MACD Fast", minval=8, maxval=20, group=group_momentum)
macd_slow = input.int(26, "MACD Slow", minval=20, maxval=35, group=group_momentum)
macd_signal = input.int(9, "MACD Signal", minval=5, maxval=15, group=group_momentum)

group_volume = "📊 Volume Analysis"
volume_ma_length = input.int(20, "Volume MA Length", minval=10, maxval=50, group=group_volume)
volume_threshold = input.float(1.2, "Volume Threshold Multiplier", minval=1.0, maxval=2.0, group=group_volume)

group_risk = "🛡️ Risk Management"
atr_length = input.int(14, "ATR Length", minval=10, maxval=30, group=group_risk)
stop_loss_atr = input.float(2.5, "Stop Loss (ATR Multiplier)", minval=1.5, maxval=4.0, group=group_risk)
take_profit_atr = input.float(5.0, "Take Profit (ATR Multiplier)", minval=3.0, maxval=8.0, group=group_risk)
risk_per_trade = input.float(2.0, "Risk Per Trade (%)", minval=0.5, maxval=5.0, group=group_risk)

group_signals = "🎯 Signal Settings"
confluence_required = input.int(4, "Confluence Required (out of 6)", minval=3, maxval=6, group=group_signals)
show_signals = input.bool(true, "Show Buy/Sell Signals", group=group_signals)
show_levels = input.bool(true, "Show Support/Resistance", group=group_signals)

// 📈 TECHNICAL INDICATORS CALCULATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Exponential Moving Averages
ema_fast = ta.ema(close, ema_fast_length)
ema_slow = ta.ema(close, ema_slow_length)

// Average Directional Index (Trend Strength)
[diplus, diminus, adx] = ta.dmi(adx_length, adx_length)

// Relative Strength Index
rsi = ta.rsi(close, rsi_length)

// MACD
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// Average True Range (Volatility)
atr = ta.atr(atr_length)

// Volume Analysis
volume_ma = ta.sma(volume, volume_ma_length)
volume_above_avg = volume > (volume_ma * volume_threshold)

// Support and Resistance Levels
pivot_high = ta.pivothigh(high, 5, 5)
pivot_low = ta.pivotlow(low, 5, 5)

var float resistance_level = na
var float support_level = na

if not na(pivot_high)
    resistance_level := pivot_high
if not na(pivot_low)
    support_level := pivot_low

// 🧠 MARKET REGIME DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Determine if market is trending or ranging
trending_market = adx > adx_threshold
ranging_market = not trending_market

// Trend direction
uptrend = ema_fast > ema_slow and close > ema_fast
downtrend = ema_fast < ema_slow and close < ema_fast
sideways = not uptrend and not downtrend

// Market volatility adaptation
high_volatility = atr > ta.sma(atr, 20) * 1.5
normal_volatility = not high_volatility

// 🎯 SIGNAL GENERATION LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════

// State management for signal alternation
var string last_signal = "NONE"
var float entry_price = na
var float stop_loss_price = na
var float take_profit_price = na

// BUY Signal Conditions (Confluence System)
buy_condition_1 = uptrend  // Trend alignment
buy_condition_2 = rsi > rsi_oversold and rsi < 60  // RSI not overbought, recovering
buy_condition_3 = macd_line > signal_line and histogram > histogram[1]  // MACD bullish
buy_condition_4 = volume_above_avg  // Volume confirmation
buy_condition_5 = trending_market  // Strong trend present
buy_condition_6 = close > support_level or na(support_level)  // Above support

// Count true conditions for BUY signal
buy_score = 0
buy_score += buy_condition_1 ? 1 : 0
buy_score += buy_condition_2 ? 1 : 0
buy_score += buy_condition_3 ? 1 : 0
buy_score += buy_condition_4 ? 1 : 0
buy_score += buy_condition_5 ? 1 : 0
buy_score += buy_condition_6 ? 1 : 0

// SELL Signal Conditions (Confluence System)
sell_condition_1 = downtrend or rsi > rsi_overbought  // Trend reversal or overbought
sell_condition_2 = macd_line < signal_line or histogram < histogram[1]  // MACD bearish
sell_condition_3 = close < resistance_level or na(resistance_level)  // Below resistance
sell_condition_4 = not na(entry_price) and close > entry_price * 1.05  // Profit protection
sell_condition_5 = rsi > 75  // Extreme overbought
sell_condition_6 = volume_above_avg  // Volume confirmation

// Count true conditions for SELL signal
sell_score = 0
sell_score += sell_condition_1 ? 1 : 0
sell_score += sell_condition_2 ? 1 : 0
sell_score += sell_condition_3 ? 1 : 0
sell_score += sell_condition_4 ? 1 : 0
sell_score += sell_condition_5 ? 1 : 0
sell_score += sell_condition_6 ? 1 : 0

// Signal Generation with Strict Alternation
generate_buy_signal = buy_score >= confluence_required and last_signal != "BUY" and strategy.position_size == 0
generate_sell_signal = sell_score >= confluence_required and last_signal == "BUY" and strategy.position_size > 0

// 💰 RISK MANAGEMENT CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate position size based on risk percentage
calculate_position_size(entry_price, stop_price, risk_percent) =>
    risk_amount = strategy.equity * (risk_percent / 100)
    price_diff = math.abs(entry_price - stop_price)
    position_size = risk_amount / price_diff
    position_size

// Dynamic ATR multipliers based on market conditions
dynamic_stop_multiplier = high_volatility ? stop_loss_atr * 1.2 : stop_loss_atr
dynamic_tp_multiplier = high_volatility ? take_profit_atr * 1.2 : take_profit_atr

// 📋 STRATEGY EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════════

if generate_buy_signal
    // Calculate levels
    entry_price := close
    stop_loss_price := entry_price - (atr * dynamic_stop_multiplier)
    take_profit_price := entry_price + (atr * dynamic_tp_multiplier)
    
    // Calculate position size
    position_size = calculate_position_size(entry_price, stop_loss_price, risk_per_trade)
    
    // Execute trade with alert messages
    strategy.entry("BUY", strategy.long, qty=position_size,
                   alert_message="UCAS BUY Signal: Entry at " + str.tostring(close) + " | SL: " + str.tostring(stop_loss_price) + " | TP: " + str.tostring(take_profit_price) + " | Score: " + str.tostring(buy_score) + "/6")
    strategy.exit("BUY_EXIT", "BUY", stop=stop_loss_price, limit=take_profit_price,
                  alert_message="UCAS Exit: SL/TP triggered")
    
    // Update state
    last_signal := "BUY"

if generate_sell_signal
    // Close position with alert message
    strategy.close("BUY", comment="SELL Signal",
                   alert_message="UCAS SELL Signal: Manual exit at " + str.tostring(close) + " | Score: " + str.tostring(sell_score) + "/6")

    // Update state
    last_signal := "SELL"
    entry_price := na
    stop_loss_price := na
    take_profit_price := na

// 🎨 VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot EMAs
plot(ema_fast, "Fast EMA", color=color.blue, linewidth=2)
plot(ema_slow, "Slow EMA", color=color.red, linewidth=2)

// Plot Support/Resistance
plot(show_levels and not na(support_level) ? support_level : na, "Support", color=color.green, style=plot.style_line, linewidth=1)
plot(show_levels and not na(resistance_level) ? resistance_level : na, "Resistance", color=color.red, style=plot.style_line, linewidth=1)

// Plot Signals
plotshape(show_signals and generate_buy_signal, "BUY Signal", shape.triangleup, location.belowbar, color.lime, size=size.normal)
plotshape(show_signals and generate_sell_signal, "SELL Signal", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Plot Stop Loss and Take Profit levels
plot(strategy.position_size > 0 and not na(stop_loss_price) ? stop_loss_price : na, "Stop Loss", color=color.red, style=plot.style_cross)
plot(strategy.position_size > 0 and not na(take_profit_price) ? take_profit_price : na, "Take Profit", color=color.green, style=plot.style_cross)

// 🔔 ALERT CONDITIONS (For Strategies, use strategy.entry/exit alerts)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Strategy alerts are automatically generated when orders are placed
// Custom alert messages can be added to strategy.entry() and strategy.exit() calls

// 📊 TABLE DISPLAY (Strategy Information)
// ═══════════════════════════════════════════════════════════════════════════════════════

if barstate.islast
    var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "Strategy", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "UCAS v6.0", text_color=color.black)
    table.cell(info_table, 0, 1, "Last Signal", text_color=color.black)
    table.cell(info_table, 1, 1, last_signal, text_color=last_signal == "BUY" ? color.green : color.red)
    table.cell(info_table, 0, 2, "Market Regime", text_color=color.black)
    table.cell(info_table, 1, 2, trending_market ? "TRENDING" : "RANGING", text_color=color.black)
    table.cell(info_table, 0, 3, "Trend", text_color=color.black)
    table.cell(info_table, 1, 3, uptrend ? "UP" : downtrend ? "DOWN" : "SIDEWAYS",
               text_color=uptrend ? color.green : downtrend ? color.red : color.orange)
    table.cell(info_table, 0, 4, "RSI", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(math.round(rsi, 1)), text_color=color.black)
    table.cell(info_table, 0, 5, "ADX", text_color=color.black)
    table.cell(info_table, 1, 5, str.tostring(math.round(adx, 1)), text_color=color.black)
    table.cell(info_table, 0, 6, "Buy Score", text_color=color.black)
    table.cell(info_table, 1, 6, str.tostring(buy_score) + "/6", text_color=buy_score >= confluence_required ? color.green : color.black)
    table.cell(info_table, 0, 7, "Sell Score", text_color=color.black)
    table.cell(info_table, 1, 7, str.tostring(sell_score) + "/6", text_color=sell_score >= confluence_required ? color.red : color.black)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 📚 STRATEGY DOCUMENTATION & USAGE GUIDE
// ═══════════════════════════════════════════════════════════════════════════════════════
//
// 🎯 PERFORMANCE TARGET: 10%+ Monthly Returns
//
// 📋 SETUP INSTRUCTIONS:
// 1. Apply to any major cryptocurrency chart (BTC, ETH, MATIC, etc.)
// 2. Set timeframe to 1D for optimal swing trading results
// 3. Adjust risk per trade (default 2%) based on your risk tolerance
// 4. Enable alerts for automated signal notifications
// 5. Backtest on historical data to validate performance
//
// 🔧 PARAMETER OPTIMIZATION:
// - For volatile markets: Increase ATR multipliers and reduce confluence requirement
// - For stable markets: Decrease ATR multipliers and increase confluence requirement
// - For faster signals: Reduce EMA lengths and RSI periods
// - For more conservative signals: Increase confluence requirement to 5-6
//
// 📊 SIGNAL INTERPRETATION:
// - GREEN TRIANGLE UP: Strong BUY signal with high confluence
// - RED TRIANGLE DOWN: SELL signal for profit taking or loss prevention
// - Signals strictly alternate - no duplicate signals possible
// - Check confluence score in table for signal strength
//
// 🛡️ RISK MANAGEMENT FEATURES:
// - Automatic stop loss at 2.5x ATR below entry
// - Take profit at 5x ATR above entry (2:1 risk-reward minimum)
// - Position sizing based on account risk percentage
// - Dynamic adjustments for market volatility
//
// 📈 MARKET ADAPTABILITY:
// - Trending markets: Uses trend-following signals with momentum confirmation
// - Ranging markets: Focuses on support/resistance bounces
// - High volatility: Increases stop distances and position size adjustments
// - Low volatility: Tightens stops for better risk management
//
// ⚠️ IMPORTANT NOTES:
// - Always backtest before live trading
// - Monitor confluence scores for signal quality
// - Adjust parameters based on specific cryptocurrency characteristics
// - Use proper position sizing to achieve target returns safely
// - Consider market conditions and news events
//
// 🔔 ALERT SETUP:
// - Strategy automatically generates alerts on order execution
// - BUY alerts include entry price, stop loss, take profit, and confluence score
// - SELL alerts include exit price and confluence score
// - Set up webhook URLs for automated trading integration
// - Enable strategy alerts in TradingView alert panel
//
// 📞 SUPPORT:
// - Strategy designed for universal crypto compatibility
// - Optimized for daily timeframe swing trading
// - Suitable for both manual and automated trading
// - Regular updates and optimizations recommended
//
// ═══════════════════════════════════════════════════════════════════════════════════════
